
import { supabase } from "@/integrations/supabase/client";
import { volcanoService } from "./volcanoService";

export class TextinService {
  private async callEdgeFunction(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    const { data, error } = await supabase.functions.invoke('textin-convert', {
      body: formData
    });

    if (error) {
      console.error('Textin API调用失败:', error);
      throw new Error(error.message || "文档解析失败");
    }

    if (data.error) {
      throw new Error(data.error);
    }

    return data.markdown;
  }

  private async callVolcanoFunction(file: File): Promise<string> {
    const result = await volcanoService.convertDocument(file);
    return result.markdown;
  }

  async convertToMarkdown(file: File): Promise<string> {
    try {
      const apiProvider = import.meta.env.VITE_API_PROVIDER || 'supabase';

      let markdown: string;
      if (apiProvider === 'volcano') {
        console.log('使用火山引擎进行文档转换');
        markdown = await this.callVolcanoFunction(file);
      } else {
        console.log('使用Supabase Edge Function进行文档转换');
        markdown = await this.callEdgeFunction(file);
      }

      return markdown;
    } catch (error) {
      console.error('文档转换失败:', error);
      throw error;
    }
  }
}

export const textinService = new TextinService();
