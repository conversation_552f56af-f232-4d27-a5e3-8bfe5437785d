# 火山引擎云函数打包脚本
# 使用方法：在 volcano-functions 目录下运行此脚本

Write-Host "🚀 开始打包火山引擎云函数..." -ForegroundColor Green

# 检查当前目录
if (-not (Test-Path "doubao-ai") -or -not (Test-Path "textin-convert")) {
    Write-Host "❌ 错误：请在 volcano-functions 目录下运行此脚本" -ForegroundColor Red
    exit 1
}

# 打包 doubao-ai 函数
Write-Host "📦 正在打包 doubao-ai 函数..." -ForegroundColor Yellow

Set-Location doubao-ai

# 检查必需文件是否存在
$requiredFiles = @("index.js", "doubaoService.js", "textProcessing.js", "prompts", "utils", "node_modules", "package.json")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "❌ doubao-ai 缺少以下文件：$($missingFiles -join ', ')" -ForegroundColor Red
    Write-Host "请先运行 'npm install' 和 'npm run build'" -ForegroundColor Yellow
    Set-Location ..
    exit 1
}

# 创建 doubao-ai 部署包
Write-Host "✅ 所有文件检查通过，正在创建 doubao-ai.zip..." -ForegroundColor Green
Compress-Archive -Path index.js,doubaoService.js,textProcessing.js,prompts,utils,node_modules,package.json -DestinationPath ../doubao-ai.zip -Force

Set-Location ..

# 打包 textin-convert 函数
Write-Host "📦 正在打包 textin-convert 函数..." -ForegroundColor Yellow

Set-Location textin-convert

# 检查必需文件是否存在
$requiredFiles = @("index.js", "node_modules", "package.json")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "❌ textin-convert 缺少以下文件：$($missingFiles -join ', ')" -ForegroundColor Red
    Write-Host "请先运行 'npm install' 和 'npm run build'" -ForegroundColor Yellow
    Set-Location ..
    exit 1
}

# 创建 textin-convert 部署包
Write-Host "✅ 所有文件检查通过，正在创建 textin-convert.zip..." -ForegroundColor Green
Compress-Archive -Path index.js,node_modules,package.json -DestinationPath ../textin-convert.zip -Force

Set-Location ..

Write-Host "🎉 打包完成！" -ForegroundColor Green
Write-Host "📁 生成的部署包：" -ForegroundColor Cyan
Write-Host "   - doubao-ai.zip" -ForegroundColor White
Write-Host "   - textin-convert.zip" -ForegroundColor White
Write-Host ""
Write-Host "📋 下一步：" -ForegroundColor Cyan
Write-Host "1. 登录火山引擎控制台" -ForegroundColor White
Write-Host "2. 创建云函数，入口函数设置为：index.handler" -ForegroundColor White
Write-Host "3. 上传对应的 zip 文件" -ForegroundColor White
Write-Host "4. 配置环境变量" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  重要提醒：入口函数必须设置为 'index.handler'，不是 'dist/index.handler'" -ForegroundColor Yellow
