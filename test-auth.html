<!DOCTYPE html>
<html>
<head>
    <title>测试认证状态</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>测试认证状态</h1>
    <div id="result"></div>
    
    <script>
        const supabaseUrl = 'https://jgidblaeongxfjavsnhy.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpnaWRibGFlb25neGZqYXZzbmh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MTQ2MTksImV4cCI6MjA2NDE5MDYxOX0.RG455r7d1rSeY9V9cobz72caWQXHd5w20_FEpQrZHAA';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        async function testAuth() {
            const resultDiv = document.getElementById('result');
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                
                if (session) {
                    resultDiv.innerHTML = `
                        <h2>✅ 用户已登录</h2>
                        <p><strong>用户ID:</strong> ${session.user.id}</p>
                        <p><strong>邮箱:</strong> ${session.user.email}</p>
                        <p><strong>访问令牌:</strong> ${session.access_token.substring(0, 50)}...</p>
                        <button onclick="testAPI()">测试API调用</button>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h2>❌ 用户未登录</h2>
                        <p>请先登录应用</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h2>❌ 错误</h2>
                    <p>${error.message}</p>
                `;
            }
        }
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                
                const response = await fetch('https://sd1mdl74gijnb7ntvsi00.apigateway-cn-guangzhou.volceapi.com/ai-service', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'generateTopics',
                        data: { domain: '测试' },
                        authorization: session.access_token
                    })
                });
                
                const result = await response.text();
                
                resultDiv.innerHTML += `
                    <h3>API测试结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>响应:</strong> ${result}</p>
                `;
                
            } catch (error) {
                resultDiv.innerHTML += `
                    <h3>❌ API测试失败:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
        
        // 页面加载时测试
        testAuth();
    </script>
</body>
</html>
