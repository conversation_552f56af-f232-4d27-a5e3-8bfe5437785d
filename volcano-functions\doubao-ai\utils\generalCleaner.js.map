{"version": 3, "file": "generalCleaner.js", "sourceRoot": "", "sources": ["../src/utils/generalCleaner.ts"], "names": [], "mappings": ";;AAGA,0DAmCC;AAtCD,yDAA8D;AAE9D,kFAAkF;AAClF,SAAgB,uBAAuB,CAAC,IAAY;IAClD,IAAI,WAAW,GAAG,IAAA,2CAAwB,EAAC,IAAI,CAAC,CAAC;IAEjD,kDAAkD;IAClD,MAAM,aAAa,GAAG;QACpB,kCAAkC;QAClC,gBAAgB;QAChB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,eAAe;KAChB,CAAC;IAEF,8BAA8B;IAC9B,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;QACpC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,WAAW;QAChB,qCAAqC;SACpC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;SACxB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;QACvB,2CAA2C;SAC1C,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;SACxB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;SACtB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACtB,wCAAwC;SACvC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAE,oBAAoB;SAClD,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAE,6BAA6B;SACjE,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAE,4BAA4B;SACxD,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAE,+BAA+B;SACtE,IAAI,EAAE,CAAC;AACZ,CAAC"}