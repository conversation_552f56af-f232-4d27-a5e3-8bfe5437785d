"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const doubaoService_1 = require("./doubaoService");
const textProcessing_1 = require("./textProcessing");
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};
// 火山引擎云函数入口
const handler = async (event, context) => {
    console.log('Volcano Engine Function - doubao-ai started');
    console.log('Event:', JSON.stringify(event, null, 2));

    // 处理 OPTIONS 请求
    if (event.httpMethod === 'OPTIONS' || event.method === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: ''
        };
    }
    try {
        // 解析请求体 - 火山引擎的 HTTP 触发器格式
        let requestBody;
        try {
            // 火山引擎 HTTP 触发器会将请求体放在 event.body 中
            if (event.body) {
                requestBody = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            } else if (event.data) {
                // 如果是控制台测试，数据可能在 event.data 中
                requestBody = event.data;
            } else {
                // 直接使用 event 作为请求体（兼容不同的触发器格式）
                requestBody = event;
            }
        }
        catch (error) {
            console.error('JSON parse error:', error);
            throw new Error('Invalid JSON in request body');
        }

        console.log('Parsed request body:', JSON.stringify(requestBody, null, 2));

        const { action, data, authorization } = requestBody;

        // 验证必要参数
        if (!action) {
            throw new Error('Missing action parameter');
        }

        if (!data) {
            throw new Error('Missing data parameter');
        }

        // 验证授权头（在生产环境中可以启用）
        // if (!authorization) {
        //   throw new Error('No authorization header');
        // }

        // 这里可以添加用户验证逻辑，暂时跳过
        // 在实际部署时，可以集成火山引擎的身份验证服务
        console.log(`Processing request: ${action} using Doubao model`);
        let result;
        const doubaoService = new doubaoService_1.DoubaoService();
        switch (action) {
            case 'generateTopics':
                result = await doubaoService.generateTopics(data.domain);
                break;
            case 'optimize':
                result = await doubaoService.optimizeContent(data.text, data.platform, data.style);
                break;
            case 'generateVideoScript':
                result = await doubaoService.generateVideoScript(data.text, data.platform, data.style);
                break;
            case 'generateContent':
                result = await doubaoService.generateContentByDomain(data.domain);
                break;
            case 'generateContentByTopic':
                result = await doubaoService.generateContentByTopic(data.topic);
                break;
            case 'generateContentByTopicStream':
                // 对于流式生成，暂时使用非流式方式返回结果
                const contents = await doubaoService.generateContentByTopic(data.topic);
                // 返回第一条内容作为单条文案，并进行清理
                const rawContent = contents.length > 0 ? contents[0] : '';
                result = (0, textProcessing_1.cleanXiaohongshuContent)(rawContent);
                break;
            case 'popularize':
                result = await doubaoService.popularizeContent(data.content);
                break;
            case 'extractDocumentSummary':
                result = await doubaoService.extractDocumentSummary(data.content);
                break;
            default:
                throw new Error('Invalid action');
        }
        return {
            statusCode: 200,
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ result })
        };
    }
    catch (error) {
        console.error('Error in doubao-ai function:', error);
        return {
            statusCode: 500,
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ error: error.message })
        };
    }
};
exports.handler = handler;
//# sourceMappingURL=index.js.map