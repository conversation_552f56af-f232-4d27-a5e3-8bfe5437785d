"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateContentByTopicPrompt = void 0;
const generateContentByTopicPrompt = (topic) => {
    return `你是一位专业的小红书法律科普KOL。请根据以下选题，直接输出一篇完整的小红书文案，不要包含任何前言、解释或确认语句。

选题：${topic}

要求：
1. 直接以文案标题开始，使用表情符号
2. 内容结构：标题 + 开篇钩子 + 核心干货 + 避坑指南 + 互动号召 + 话题标签
3. 字数控制在300-800字
4. 大量使用表情符号、加粗、数字列表
5. 语言风格轻松活泼，符合小红书用户喜好
6. 结尾包含5-8个相关话题标签

请直接输出文案内容，不要包含"好的"、"用户输入"、"AI输出"等说明性文字。`;
};
exports.generateContentByTopicPrompt = generateContentByTopicPrompt;
//# sourceMappingURL=generateContentByTopicPrompt.js.map