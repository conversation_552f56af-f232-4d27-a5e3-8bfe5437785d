"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanAIGeneratedContent = exports.cleanXiaohongshuContent = exports.parseGeneratedContent = exports.parseTopics = exports.cleanMarkdownText = void 0;
// Main text processing module - exports all text processing functions
var markdownCleaning_1 = require("./utils/markdownCleaning");
Object.defineProperty(exports, "cleanMarkdownText", { enumerable: true, get: function () { return markdownCleaning_1.cleanMarkdownText; } });
var contentParsing_1 = require("./utils/contentParsing");
Object.defineProperty(exports, "parseTopics", { enumerable: true, get: function () { return contentParsing_1.parseTopics; } });
Object.defineProperty(exports, "parseGeneratedContent", { enumerable: true, get: function () { return contentParsing_1.parseGeneratedContent; } });
var xiaohongshuCleaner_1 = require("./utils/xiaohongshuCleaner");
Object.defineProperty(exports, "cleanXiaohongshuContent", { enumerable: true, get: function () { return xiaohongshuCleaner_1.cleanXiaohongshuContent; } });
var generalCleaner_1 = require("./utils/generalCleaner");
Object.defineProperty(exports, "cleanAIGeneratedContent", { enumerable: true, get: function () { return generalCleaner_1.cleanAIGeneratedContent; } });
//# sourceMappingURL=textProcessing.js.map