# 🌋 火山引擎云函数部署指南

## ⚠️ 重要提醒

**如果您遇到 `function_start_failed` 错误，请确保：**
1. 入口函数配置为 `index.handler`（不是 `dist/index.handler`）
2. 部署包的根目录包含 `index.js` 文件
3. 按照本文档的打包说明正确打包文件

本文档说明如何将法律文案生成系统的云函数部署到火山引擎平台。

## 目录结构

```
volcano-functions/
├── doubao-ai/                 # Doubao AI服务云函数
│   ├── src/
│   │   ├── index.ts          # 入口文件
│   │   ├── doubaoService.ts  # Doubao服务类
│   │   ├── textProcessing.ts # 文本处理模块
│   │   ├── prompts/          # 提示词模块
│   │   └── utils/            # 工具函数
│   ├── package.json
│   └── tsconfig.json
└── textin-convert/            # Textin文档转换云函数
    ├── src/
    │   └── index.ts          # 入口文件
    ├── package.json
    └── tsconfig.json
```

## 环境变量配置

### 1. Doubao AI 云函数环境变量

在火山引擎云函数控制台中配置以下环境变量：

```bash
DOUBAO_API_KEY=your_doubao_api_key_here
```

### 2. Textin Convert 云函数环境变量

```bash
TEXTIN_APP_ID=your_textin_app_id_here
TEXTIN_SECRET_CODE=your_textin_secret_code_here
```

## 部署步骤

### 1. 准备部署包（重要！）

#### 🎯 关键说明

**重要**：火山引擎云函数需要在部署包的**根目录**找到 `index.js` 文件，不能在子目录中！

#### 编译代码

**Doubao AI 函数**：
```bash
cd volcano-functions/doubao-ai
npm install
npm run build
```

**Textin Convert 函数**：
```bash
cd volcano-functions/textin-convert
npm install
npm run build
```

#### 📋 打包文件清单

编译完成后，您会看到以下文件结构：

**Doubao AI 函数目录结构**：
```
volcano-functions/doubao-ai/
├── index.js              ← ✅ 必须包含（入口文件）
├── doubaoService.js      ← ✅ 必须包含
├── textProcessing.js     ← ✅ 必须包含
├── prompts/              ← ✅ 必须包含整个目录
│   ├── index.js
│   ├── generateTopicsPrompt.js
│   ├── optimizeContentPrompt.js
│   └── ... (其他prompt文件)
├── utils/                ← ✅ 必须包含整个目录
│   ├── contentParsing.js
│   ├── markdownCleaning.js
│   └── ... (其他工具文件)
├── node_modules/         ← ✅ 必须包含整个目录
├── package.json          ← ✅ 必须包含
├── src/                  ← ❌ 不需要包含
├── dist/                 ← ❌ 不需要包含
└── tsconfig.json         ← ❌ 不需要包含
```

**Textin Convert 函数目录结构**：
```
volcano-functions/textin-convert/
├── index.js              ← ✅ 必须包含（入口文件）
├── node_modules/         ← ✅ 必须包含整个目录
├── package.json          ← ✅ 必须包含
├── src/                  ← ❌ 不需要包含
├── dist/                 ← ❌ 不需要包含
└── tsconfig.json         ← ❌ 不需要包含
```

#### 🗂️ 具体打包步骤

**方法一：手动选择文件打包（推荐）**

**Doubao AI 函数**：
1. 进入 `volcano-functions/doubao-ai/` 目录
2. 选择以下文件和文件夹：
   - ✅ `index.js`
   - ✅ `doubaoService.js`
   - ✅ `textProcessing.js`
   - ✅ `prompts/` 文件夹
   - ✅ `utils/` 文件夹
   - ✅ `node_modules/` 文件夹
   - ✅ `package.json`
3. 右键选择"发送到" → "压缩文件夹"或使用压缩软件打包成 `doubao-ai.zip`

**Textin Convert 函数**：
1. 进入 `volcano-functions/textin-convert/` 目录
2. 选择以下文件和文件夹：
   - ✅ `index.js`
   - ✅ `node_modules/` 文件夹
   - ✅ `package.json`
3. 打包成 `textin-convert.zip`

**方法二：使用命令行打包**

Windows PowerShell：
```powershell
# Doubao AI 函数
cd volcano-functions/doubao-ai
Compress-Archive -Path index.js,doubaoService.js,textProcessing.js,prompts,utils,node_modules,package.json -DestinationPath doubao-ai.zip

# Textin Convert 函数
cd ../textin-convert
Compress-Archive -Path index.js,node_modules,package.json -DestinationPath textin-convert.zip
```

**方法三：使用自动打包脚本（最简单）**

我们提供了一个自动打包脚本，在 `volcano-functions` 目录下运行：

```powershell
# 在 volcano-functions 目录下
.\package-for-deployment.ps1
```

这个脚本会：
- 自动检查所有必需文件是否存在
- 创建正确的部署包
- 生成 `doubao-ai.zip` 和 `textin-convert.zip`

### 2. 在火山引擎控制台创建云函数

1. 登录火山引擎控制台
2. 进入云函数服务
3. 创建新函数：

#### Doubao AI 函数配置
- 函数名称：`doubao-ai`
- 运行时：`Node.js 18.x`
- **入口函数：`index.handler`**（⚠️ 重要：不是 `dist/index.handler`）
- 超时时间：`300秒`（5分钟）
- 内存：`512MB`

#### Textin Convert 函数配置
- 函数名称：`textin-convert`
- 运行时：`Node.js 18.x`
- **入口函数：`index.handler`**（⚠️ 重要：不是 `dist/index.handler`）
- 超时时间：`300秒`（5分钟）
- 内存：`1024MB`（文档处理需要更多内存）

### 3. 上传代码包

1. 在函数详情页面，选择"代码"选项卡
2. 上传准备好的zip包
3. 保存并发布

### 4. 配置触发器

为每个函数配置HTTP触发器：

1. 在函数详情页面，选择"触发器"选项卡
2. 添加HTTP触发器
3. 配置路径：
   - Doubao AI: `/doubao-ai`
   - Textin Convert: `/textin-convert`
4. 方法：POST
5. 认证方式：无认证（或根据需要配置）

### 5. 获取函数URL

部署完成后，在触发器配置中获取函数的访问URL，格式类似：
```
https://your-function-id.volcano-engine.com/doubao-ai
https://your-function-id.volcano-engine.com/textin-convert
```

## 前端配置

### 更新环境变量

在项目根目录的 `.env` 文件中配置：

```bash
VITE_API_PROVIDER=volcano
VITE_VOLCANO_DOUBAO_AI_URL=https://your-function-id.volcano-engine.com/doubao-ai
VITE_VOLCANO_TEXTIN_CONVERT_URL=https://your-function-id.volcano-engine.com/textin-convert
```

### 验证配置

1. 启动前端应用：
```bash
npm run dev
```

2. 测试功能：
   - 生成法律选题
   - 上传文档转换
   - 内容优化等功能

## 监控和日志

### 查看函数日志

1. 在火山引擎控制台进入云函数详情页
2. 选择"监控"选项卡
3. 查看函数调用日志和性能指标

### 常见问题排查

1. **函数超时**：增加超时时间配置
2. **内存不足**：增加内存配置
3. **API调用失败**：检查环境变量配置
4. **CORS问题**：确保函数返回正确的CORS头

## 成本优化

1. **按需调用**：云函数按调用次数和执行时间计费
2. **内存配置**：根据实际需要调整内存大小
3. **并发控制**：设置合适的并发限制

## 安全配置

1. **环境变量**：敏感信息通过环境变量配置
2. **访问控制**：根据需要配置函数访问权限
3. **HTTPS**：确保使用HTTPS访问函数

## 备份和恢复

1. **代码备份**：定期备份函数代码
2. **配置备份**：记录环境变量和触发器配置
3. **版本管理**：使用版本控制管理函数代码

## 性能优化

1. **冷启动优化**：保持函数温热状态
2. **代码优化**：减少依赖包大小
3. **缓存策略**：合理使用缓存机制

## 故障排除

### 常见错误及解决方案

1. **环境变量未配置**
   - 错误：`DOUBAO_API_KEY environment variable is required`
   - 解决：在云函数控制台配置相应环境变量

2. **文件上传失败**
   - 错误：文件格式不支持或大小超限
   - 解决：检查文件格式和大小限制

3. **API调用超时**
   - 错误：请求超时
   - 解决：增加函数超时时间配置

## 联系支持

如遇到部署问题，请联系技术支持或查看火山引擎官方文档。
