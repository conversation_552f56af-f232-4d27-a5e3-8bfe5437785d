import { 
  generateTopicsPrompt, 
  optimizeContentPrompt, 
  generateContentPrompt, 
  generateContentByTopicPrompt, 
  popularizeContentPrompt, 
  extractDocumentSummaryPrompt,
  generateVideoScriptPrompt
} from './prompts'
import { parseTopics, parseGeneratedContent, cleanXiaohongshuContent, cleanAIGeneratedContent } from './textProcessing'

export class DoubaoService {
  private apiKey: string
  private baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
  private model = 'doubao-seed-1-6-thinking-250615'

  constructor() {
    const apiKey = process.env.DOUBAO_API_KEY
    if (!apiKey) {
      throw new Error('DOUBAO_API_KEY environment variable is required')
    }
    this.apiKey = apiKey
  }

  private async callDoubaoAPI(input: string): Promise<any> {
    try {
      const requestBody = {
        model: this.model,
        messages: [
          {
            role: "user",
            content: input
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      }
      
      console.log('Calling Doubao Chat API with body:', JSON.stringify(requestBody, null, 2))
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      console.log('Doubao API response status:', response.status)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Doubao API error:', response.status, errorText)
        throw new Error(`Doubao API error: ${response.status} ${errorText}`)
      }

      const result = await response.json()
      console.log('Doubao API response:', JSON.stringify(result, null, 2))
      
      // 从Chat API的响应中提取文本内容
      let content = ''
      if (result.choices && Array.isArray(result.choices) && result.choices.length > 0) {
        const choice = result.choices[0]
        if (choice.message && choice.message.content) {
          content = choice.message.content
        }
      }
      
      console.log('Extracted content:', content)
      return content || ''
    } catch (error) {
      console.error('Error calling Doubao API:', error)
      throw new Error(`Failed to call Doubao API: ${(error as Error).message}`)
    }
  }

  async generateTopics(domain: string): Promise<string[]> {
    try {
      const prompt = generateTopicsPrompt(domain)
      console.log('Generating topics for domain:', domain)

      const rawText = await this.callDoubaoAPI(prompt)
      console.log('Raw response for topics:', rawText.substring(0, 200) + '...')
      
      const topics = parseTopics(rawText)
      console.log('Parsed topics:', topics.length, 'topics found')
      
      return topics
    } catch (error) {
      console.error('Error generating topics:', error)
      throw new Error(`Failed to generate topics: ${(error as Error).message}`)
    }
  }

  async optimizeContent(text: string, platform: string, style: string): Promise<string> {
    try {
      const prompt = optimizeContentPrompt(text, platform, style)
      console.log('Optimizing content, length:', text.length)

      const optimizedText = await this.callDoubaoAPI(prompt)
      const cleanedText = cleanAIGeneratedContent(optimizedText)
      console.log('Optimized content generated, length:', cleanedText.length)
      
      return cleanedText
    } catch (error) {
      console.error('Error optimizing content:', error)
      throw new Error(`Failed to optimize content: ${(error as Error).message}`)
    }
  }

  async generateVideoScript(text: string, platform: string, style: string): Promise<string> {
    try {
      const prompt = generateVideoScriptPrompt(text, platform, style)
      console.log('Generating video script, length:', text.length)

      const videoScript = await this.callDoubaoAPI(prompt)
      const cleanedScript = cleanAIGeneratedContent(videoScript)
      console.log('Video script generated, length:', cleanedScript.length)
      
      return cleanedScript
    } catch (error) {
      console.error('Error generating video script:', error)
      throw new Error(`Failed to generate video script: ${(error as Error).message}`)
    }
  }

  async generateContentByDomain(domain: string): Promise<string[]> {
    try {
      const prompt = generateContentPrompt(domain)
      console.log('Generating content for domain:', domain)

      const rawText = await this.callDoubaoAPI(prompt)
      console.log('Raw response for content generation:', rawText.substring(0, 200) + '...')
      
      const contents = parseGeneratedContent(rawText)
      console.log('Generated contents:', contents.length, 'items')
      
      return contents.map(content => cleanAIGeneratedContent(content))
    } catch (error) {
      console.error('Error generating content:', error)
      throw new Error(`Failed to generate content: ${(error as Error).message}`)
    }
  }

  async generateContentByTopic(topic: string): Promise<string[]> {
    try {
      const prompt = generateContentByTopicPrompt(topic)
      console.log('Generating content for topic:', topic)

      const rawText = await this.callDoubaoAPI(prompt)
      console.log('Raw response for topic content:', rawText.substring(0, 200) + '...')
      
      // 对于小红书文案，使用专门的清理函数
      const cleanedContent = cleanXiaohongshuContent(rawText)
      console.log('Cleaned xiaohongshu content, length:', cleanedContent.length)
      
      // 返回单条文案内容
      return [cleanedContent]
    } catch (error) {
      console.error('Error generating content by topic:', error)
      throw new Error(`Failed to generate content by topic: ${(error as Error).message}`)
    }
  }

  async popularizeContent(content: string): Promise<string> {
    try {
      const prompt = popularizeContentPrompt(content)
      console.log('Popularizing content, length:', content.length)

      const popularizedText = await this.callDoubaoAPI(prompt)
      const cleanedText = cleanAIGeneratedContent(popularizedText)
      console.log('Popularized content generated, length:', cleanedText.length)
      
      return cleanedText
    } catch (error) {
      console.error('Error popularizing content:', error)
      throw new Error(`Failed to popularize content: ${(error as Error).message}`)
    }
  }

  async extractDocumentSummary(content: string): Promise<any> {
    try {
      const prompt = extractDocumentSummaryPrompt(content)
      console.log('Extracting document summary, content length:', content.length)

      const summaryText = await this.callDoubaoAPI(prompt)
      console.log('Document summary extracted, length:', summaryText.length)

      // 解析新的结构化摘要格式
      const summary = this.parseStructuredSummary(summaryText)

      return summary
    } catch (error) {
      console.error('Error extracting document summary:', error)
      throw new Error(`Failed to extract document summary: ${(error as Error).message}`)
    }
  }

  private parseStructuredSummary(summaryText: string): any {
    // 检查是否为错误情况
    if (summaryText.includes('未能从文书中提炼出完整的争议焦点与裁判要点')) {
      return {
        error: '文档内容不足，无法提取有效的争议焦点与裁判要点',
        suggestion: '请确认文档是否为完整的判决书或包含足够的法律分析内容'
      }
    }

    // 解析结构化列表
    const disputePoints = []
    let basicInfo = ''
    let legalAnalysis = ''
    let socialImpact = ''

    // 匹配格式：数字. [类型] 争议描述 [裁判要点] 要点内容
    const listPattern = /(\d+)\.\s*\[([^\]]+)\]\s*([^\n\[]+)\s*\[裁判要点\]\s*([^\n]+(?:\n(?!\d+\.)[^\n]*)*)/g

    let match
    while ((match = listPattern.exec(summaryText)) !== null) {
      const [, number, disputeType, disputeDescription, rulingPoint] = match

      disputePoints.push({
        序号: parseInt(number),
        争议类型: disputeType.trim(),
        争议描述: disputeDescription.trim(),
        裁判要点: rulingPoint.trim().replace(/\n\s*/g, ' ')
      })
    }

    // 如果没有匹配到结构化格式，尝试简单分段处理
    if (disputePoints.length === 0) {
      const sections = summaryText.split(/\n\s*\n/).filter(section => section.trim())
      if (sections.length >= 3) {
        basicInfo = sections[0].trim()
        legalAnalysis = sections[1].trim()
        socialImpact = sections[2].trim()
      } else if (sections.length > 0) {
        basicInfo = summaryText.trim()
      }
    } else {
      // 将争议点转换为原有的格式以保持兼容性
      basicInfo = disputePoints.filter(p => p.争议类型 === '事实争议')
        .map(p => `${p.争议描述} ${p.裁判要点}`).join('\n\n')

      legalAnalysis = disputePoints.filter(p => p.争议类型 === '法律争议')
        .map(p => `${p.争议描述} ${p.裁判要点}`).join('\n\n')

      // 如果没有明确分类，将所有争议点放入法律分析
      if (!basicInfo && disputePoints.length > 0) {
        legalAnalysis = disputePoints.map(p => `${p.争议描述} ${p.裁判要点}`).join('\n\n')
      }

      socialImpact = '本案通过法院的细致审理和精准认定，为类似争议的解决提供了重要的法律参考和实践指导。'
    }

    return {
      story: basicInfo || '案件基本情况暂无详细信息',
      legal_analysis: legalAnalysis || '法律争议分析暂无详细信息',
      social_impact: socialImpact || '社会影响分析暂无详细信息',
      raw_summary: summaryText,
      structured_disputes: disputePoints // 保留结构化数据供后续使用
    }
  }
}
