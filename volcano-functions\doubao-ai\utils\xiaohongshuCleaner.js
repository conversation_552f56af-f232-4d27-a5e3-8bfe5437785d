"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanXiaohongshuContent = cleanXiaohongshuContent;
const basicTextCleaning_1 = require("./basicTextCleaning");
const markdownCleaning_1 = require("./markdownCleaning");
// Specialized function for cleaning Xiaohongshu content generation - version that preserves hashtags
function cleanXiaohongshuContent(text) {
    if (!text)
        return '';
    // Remove system prompts first
    let cleanedText = (0, basicTextCleaning_1.removeSystemPrompts)(text);
    // Clean markdown format symbols, but preserve # symbols in hashtags
    cleanedText = (0, markdownCleaning_1.removeMarkdownButKeepHashtags)(cleanedText);
    // Remove special symbols and decorative elements
    cleanedText = (0, basicTextCleaning_1.removeSpecialSymbols)(cleanedText);
    // Remove multiple consecutive asterisks
    cleanedText = cleanedText.replace(/\*{2,}/g, '');
    // Normalize whitespace and line breaks
    cleanedText = (0, basicTextCleaning_1.normalizeWhitespace)(cleanedText);
    // If cleaned content is empty or too short, return error message
    if (cleanedText.length < 50) {
        return '生成的内容需要进一步处理，请重新生成。';
    }
    return cleanedText;
}
//# sourceMappingURL=xiaohongshuCleaner.js.map