{"version": 3, "file": "doubaoService.js", "sourceRoot": "", "sources": ["src/doubaoService.ts"], "names": [], "mappings": ";;;AAAA,uCAQkB;AAClB,qDAAuH;AAEvH,MAAa,aAAa;IAKxB;QAHQ,YAAO,GAAG,2DAA2D,CAAA;QACrE,UAAK,GAAG,iCAAiC,CAAA;QAG/C,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAA;QACzC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAA;QACpE,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAa;QACvC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,KAAK;qBACf;iBACF;gBACD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;aACjB,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;YAEvF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;gBACzC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;oBACxC,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;aAClC,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAA;YAE3D,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;gBACvC,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,QAAQ,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;gBAC9D,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC,CAAA;YACtE,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAA;YAC3C,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;YAEpE,sBAAsB;YACtB,IAAI,OAAO,GAAG,EAAE,CAAA;YAChB,IAAI,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjF,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;gBAChC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC7C,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAA;gBAClC,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAA;YAC1C,OAAO,OAAO,IAAI,EAAE,CAAA;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;YACjD,MAAM,IAAI,KAAK,CAAC,8BAA+B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QAC3E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,8BAAoB,EAAC,MAAM,CAAC,CAAA;YAC3C,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,MAAM,CAAC,CAAA;YAEpD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAE1E,MAAM,MAAM,GAAG,IAAA,4BAAW,EAAC,OAAO,CAAC,CAAA;YACnC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;YAE5D,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,MAAM,IAAI,KAAK,CAAC,8BAA+B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QAC3E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,IAAY,EAAE,QAAgB,EAAE,KAAa;QACjE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,+BAAqB,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAA;YAC3D,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YAEvD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YACtD,MAAM,WAAW,GAAG,IAAA,wCAAuB,EAAC,aAAa,CAAC,CAAA;YAC1D,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA;YAEvE,OAAO,WAAW,CAAA;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;YACjD,MAAM,IAAI,KAAK,CAAC,+BAAgC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QAC5E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,IAAY,EAAE,QAAgB,EAAE,KAAa;QACrE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,mCAAyB,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAA;YAC/D,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YAE5D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YACpD,MAAM,aAAa,GAAG,IAAA,wCAAuB,EAAC,WAAW,CAAC,CAAA;YAC1D,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAA;YAEpE,OAAO,aAAa,CAAA;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;YACtD,MAAM,IAAI,KAAK,CAAC,oCAAqC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QACjF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAC1C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,+BAAqB,EAAC,MAAM,CAAC,CAAA;YAC5C,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAA;YAErD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAEtF,MAAM,QAAQ,GAAG,IAAA,sCAAqB,EAAC,OAAO,CAAC,CAAA;YAC/C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;YAE5D,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAA,wCAAuB,EAAC,OAAO,CAAC,CAAC,CAAA;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;YACjD,MAAM,IAAI,KAAK,CAAC,+BAAgC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QAC5E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,KAAa;QACxC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,sCAA4B,EAAC,KAAK,CAAC,CAAA;YAClD,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YAEnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAEjF,oBAAoB;YACpB,MAAM,cAAc,GAAG,IAAA,wCAAuB,EAAC,OAAO,CAAC,CAAA;YACvD,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAA;YAE1E,WAAW;YACX,OAAO,CAAC,cAAc,CAAC,CAAA;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAA;YAC1D,MAAM,IAAI,KAAK,CAAC,wCAAyC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QACrF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAe;QACrC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,iCAAuB,EAAC,OAAO,CAAC,CAAA;YAC/C,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;YAE5D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YACxD,MAAM,WAAW,GAAG,IAAA,wCAAuB,EAAC,eAAe,CAAC,CAAA;YAC5D,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA;YAEzE,OAAO,WAAW,CAAA;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;YACnD,MAAM,IAAI,KAAK,CAAC,iCAAkC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QAC9E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,OAAe;QAC1C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,sCAA4B,EAAC,OAAO,CAAC,CAAA;YACpD,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;YAE3E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YACpD,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA;YAEtE,cAAc;YACd,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAA;YAExD,OAAO,OAAO,CAAA;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAA;YAC1D,MAAM,IAAI,KAAK,CAAC,uCAAwC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAA;QACpF,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,WAAmB;QAChD,YAAY;QACZ,IAAI,WAAW,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAClD,OAAO;gBACL,KAAK,EAAE,yBAAyB;gBAChC,UAAU,EAAE,4BAA4B;aACzC,CAAA;QACH,CAAC;QAED,UAAU;QACV,MAAM,aAAa,GAAG,EAAE,CAAA;QACxB,IAAI,SAAS,GAAG,EAAE,CAAA;QAClB,IAAI,aAAa,GAAG,EAAE,CAAA;QACtB,IAAI,YAAY,GAAG,EAAE,CAAA;QAErB,iCAAiC;QACjC,MAAM,WAAW,GAAG,kFAAkF,CAAA;QAEtG,IAAI,KAAK,CAAA;QACT,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACxD,MAAM,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,CAAA;YAEtE,aAAa,CAAC,IAAI,CAAC;gBACjB,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC;gBACpB,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE;gBACxB,IAAI,EAAE,kBAAkB,CAAC,IAAI,EAAE;gBAC/B,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;aAChD,CAAC,CAAA;QACJ,CAAC;QAED,wBAAwB;QACxB,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;YAC/E,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACzB,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;gBAC9B,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;gBAClC,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;YACnC,CAAC;iBAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAS,GAAG,WAAW,CAAC,IAAI,EAAE,CAAA;YAChC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,qBAAqB;YACrB,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC;iBACrD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAE/C,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC;iBACzD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAE/C,wBAAwB;YACxB,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC5E,CAAC;YAED,YAAY,GAAG,2CAA2C,CAAA;QAC5D,CAAC;QAED,OAAO;YACL,KAAK,EAAE,SAAS,IAAI,cAAc;YAClC,cAAc,EAAE,aAAa,IAAI,cAAc;YAC/C,aAAa,EAAE,YAAY,IAAI,cAAc;YAC7C,WAAW,EAAE,WAAW;YACxB,mBAAmB,EAAE,aAAa,CAAC,eAAe;SACnD,CAAA;IACH,CAAC;CACF;AA5PD,sCA4PC"}