"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateVideoScriptPrompt = exports.extractDocumentSummaryPrompt = exports.popularizeContentPrompt = exports.generateContentByTopicPrompt = exports.generateContentPrompt = exports.optimizeContentPrompt = exports.generateTopicsPrompt = void 0;
var generateTopicsPrompt_1 = require("./generateTopicsPrompt");
Object.defineProperty(exports, "generateTopicsPrompt", { enumerable: true, get: function () { return generateTopicsPrompt_1.generateTopicsPrompt; } });
var optimizeContentPrompt_1 = require("./optimizeContentPrompt");
Object.defineProperty(exports, "optimizeContentPrompt", { enumerable: true, get: function () { return optimizeContentPrompt_1.optimizeContentPrompt; } });
var generateContentPrompt_1 = require("./generateContentPrompt");
Object.defineProperty(exports, "generateContentPrompt", { enumerable: true, get: function () { return generateContentPrompt_1.generateContentPrompt; } });
var generateContentByTopicPrompt_1 = require("./generateContentByTopicPrompt");
Object.defineProperty(exports, "generateContentByTopicPrompt", { enumerable: true, get: function () { return generateContentByTopicPrompt_1.generateContentByTopicPrompt; } });
var popularizeContentPrompt_1 = require("./popularizeContentPrompt");
Object.defineProperty(exports, "popularizeContentPrompt", { enumerable: true, get: function () { return popularizeContentPrompt_1.popularizeContentPrompt; } });
var extractDocumentSummaryPrompt_1 = require("./extractDocumentSummaryPrompt");
Object.defineProperty(exports, "extractDocumentSummaryPrompt", { enumerable: true, get: function () { return extractDocumentSummaryPrompt_1.extractDocumentSummaryPrompt; } });
var generateVideoScriptPrompt_1 = require("./generateVideoScriptPrompt");
Object.defineProperty(exports, "generateVideoScriptPrompt", { enumerable: true, get: function () { return generateVideoScriptPrompt_1.generateVideoScriptPrompt; } });
//# sourceMappingURL=index.js.map