# 火山引擎云函数部署指南

本文档说明如何将法律文案生成系统的云函数部署到火山引擎平台。

## 目录结构

```
volcano-functions/
├── doubao-ai/                 # Doubao AI服务云函数
│   ├── src/
│   │   ├── index.ts          # 入口文件
│   │   ├── doubaoService.ts  # Doubao服务类
│   │   ├── textProcessing.ts # 文本处理模块
│   │   ├── prompts/          # 提示词模块
│   │   └── utils/            # 工具函数
│   ├── package.json
│   └── tsconfig.json
└── textin-convert/            # Textin文档转换云函数
    ├── src/
    │   └── index.ts          # 入口文件
    ├── package.json
    └── tsconfig.json
```

## 环境变量配置

### 1. Doubao AI 云函数环境变量

在火山引擎云函数控制台中配置以下环境变量：

```bash
DOUBAO_API_KEY=your_doubao_api_key_here
```

### 2. Textin Convert 云函数环境变量

```bash
TEXTIN_APP_ID=your_textin_app_id_here
TEXTIN_SECRET_CODE=your_textin_secret_code_here
```

## 部署步骤

### 1. 准备部署包

#### Doubao AI 函数

```bash
cd volcano-functions/doubao-ai
npm install
npm run build
```

将以下文件打包为zip：
- `dist/` 目录（编译后的JavaScript文件）
- `node_modules/` 目录
- `package.json`

#### Textin Convert 函数

```bash
cd volcano-functions/textin-convert
npm install
npm run build
```

将以下文件打包为zip：
- `dist/` 目录（编译后的JavaScript文件）
- `node_modules/` 目录
- `package.json`

### 2. 在火山引擎控制台创建云函数

1. 登录火山引擎控制台
2. 进入云函数服务
3. 创建新函数：
   - 函数名称：`doubao-ai` 和 `textin-convert`
   - 运行时：Node.js 18.x
   - 入口函数：`index.handler`
   - 超时时间：300秒（5分钟）
   - 内存：512MB

### 3. 上传代码包

1. 在函数详情页面，选择"代码"选项卡
2. 上传准备好的zip包
3. 保存并发布

### 4. 配置触发器

为每个函数配置HTTP触发器：

1. 在函数详情页面，选择"触发器"选项卡
2. 添加HTTP触发器
3. 配置路径：
   - Doubao AI: `/doubao-ai`
   - Textin Convert: `/textin-convert`
4. 方法：POST
5. 认证方式：无认证（或根据需要配置）

### 5. 获取函数URL

部署完成后，在触发器配置中获取函数的访问URL，格式类似：
```
https://your-function-id.volcano-engine.com/doubao-ai
https://your-function-id.volcano-engine.com/textin-convert
```

## 前端配置

### 更新环境变量

在项目根目录的 `.env` 文件中配置：

```bash
VITE_API_PROVIDER=volcano
VITE_VOLCANO_DOUBAO_AI_URL=https://your-function-id.volcano-engine.com/doubao-ai
VITE_VOLCANO_TEXTIN_CONVERT_URL=https://your-function-id.volcano-engine.com/textin-convert
```

### 验证配置

1. 启动前端应用：
```bash
npm run dev
```

2. 测试功能：
   - 生成法律选题
   - 上传文档转换
   - 内容优化等功能

## 监控和日志

### 查看函数日志

1. 在火山引擎控制台进入云函数详情页
2. 选择"监控"选项卡
3. 查看函数调用日志和性能指标

### 常见问题排查

1. **函数超时**：增加超时时间配置
2. **内存不足**：增加内存配置
3. **API调用失败**：检查环境变量配置
4. **CORS问题**：确保函数返回正确的CORS头

## 成本优化

1. **按需调用**：云函数按调用次数和执行时间计费
2. **内存配置**：根据实际需要调整内存大小
3. **并发控制**：设置合适的并发限制

## 安全配置

1. **环境变量**：敏感信息通过环境变量配置
2. **访问控制**：根据需要配置函数访问权限
3. **HTTPS**：确保使用HTTPS访问函数

## 备份和恢复

1. **代码备份**：定期备份函数代码
2. **配置备份**：记录环境变量和触发器配置
3. **版本管理**：使用版本控制管理函数代码

## 性能优化

1. **冷启动优化**：保持函数温热状态
2. **代码优化**：减少依赖包大小
3. **缓存策略**：合理使用缓存机制

## 故障排除

### 常见错误及解决方案

1. **环境变量未配置**
   - 错误：`DOUBAO_API_KEY environment variable is required`
   - 解决：在云函数控制台配置相应环境变量

2. **文件上传失败**
   - 错误：文件格式不支持或大小超限
   - 解决：检查文件格式和大小限制

3. **API调用超时**
   - 错误：请求超时
   - 解决：增加函数超时时间配置

## 联系支持

如遇到部署问题，请联系技术支持或查看火山引擎官方文档。
