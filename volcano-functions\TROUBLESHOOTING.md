# 🔧 火山引擎云函数部署故障排除指南

## ❌ function_start_failed 错误解决方案

### 问题描述
在火山引擎控制台看到 `function_start_failed` 错误，函数无法正常启动。

### 🎯 解决方案

#### 1. 检查入口函数配置
**错误配置**：
```
入口函数：dist/index.handler
```

**正确配置**：
```
入口函数：index.handler
```

#### 2. 确认文件结构
部署包的根目录必须包含：
```
doubao-ai/
├── index.js          ← 必须在根目录
├── doubaoService.js
├── textProcessing.js
├── prompts/
├── utils/
├── node_modules/
└── package.json
```

#### 3. 重新打包部署

我们已经修复了编译配置，现在 `index.js` 文件会直接生成在根目录。

**重新打包步骤**：
1. 确认 `volcano-functions/doubao-ai/index.js` 存在
2. 将以下文件打包成zip：
   - `index.js`（根目录）
   - `doubaoService.js`
   - `textProcessing.js`
   - `prompts/` 目录
   - `utils/` 目录
   - `node_modules/` 目录
   - `package.json`

#### 4. 重新部署
1. 删除现有的失败函数
2. 重新创建函数
3. 使用正确的入口函数配置：`index.handler`
4. 上传新的部署包

### 🔍 其他可能的原因

#### 1. 依赖包问题
**症状**：函数启动时找不到模块
**解决**：确保 `node_modules` 目录完整包含在部署包中

#### 2. 内存不足
**症状**：函数启动超时
**解决**：
- doubao-ai：建议 512MB
- textin-convert：建议 1024MB

#### 3. 环境变量缺失
**症状**：函数启动后立即报错
**解决**：确认环境变量配置正确：
```bash
# doubao-ai 需要
DOUBAO_API_KEY=your_api_key

# textin-convert 需要
TEXTIN_APP_ID=your_app_id
TEXTIN_SECRET_CODE=your_secret_code
```

#### 4. 代码语法错误
**症状**：函数无法解析
**解决**：检查编译后的 JavaScript 文件是否有语法错误

### 📋 部署检查清单

在重新部署前，请确认：

- [ ] 入口函数配置为 `index.handler`
- [ ] 部署包根目录包含 `index.js`
- [ ] 所有依赖文件都在部署包中
- [ ] 环境变量配置正确
- [ ] 内存配置足够
- [ ] 超时时间设置为 300秒

### 🧪 测试验证

部署成功后，可以通过以下方式测试：

#### 1. 控制台测试
在火山引擎控制台的"测试"页面，使用以下测试数据：

**doubao-ai 测试**：
```json
{
  "httpMethod": "POST",
  "body": "{\"action\":\"generateTopics\",\"data\":{\"domain\":\"婚姻家庭\"},\"authorization\":\"test\"}"
}
```

**textin-convert 测试**：
```json
{
  "httpMethod": "POST",
  "headers": {
    "content-type": "application/octet-stream",
    "x-file-name": "test.pdf"
  },
  "body": "test_file_content_base64",
  "isBase64Encoded": true
}
```

#### 2. 查看日志
在"监控"页面查看函数执行日志，确认：
- 函数正常启动
- 没有模块加载错误
- 环境变量读取正常

### 🆘 如果问题仍然存在

1. **检查日志**：在火山引擎控制台查看详细错误日志
2. **重新编译**：删除所有编译文件，重新执行 `npm run build`
3. **清理重建**：删除 `node_modules`，重新执行 `npm install`
4. **联系支持**：如果问题持续，联系火山引擎技术支持

### 📞 技术支持

- 火山引擎官方文档：https://www.volcengine.com/docs/
- 技术支持：通过火山引擎控制台提交工单

---

**记住**：最常见的问题是入口函数配置错误，确保使用 `index.handler` 而不是 `dist/index.handler`！
