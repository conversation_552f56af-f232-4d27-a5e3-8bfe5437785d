import { supabase } from '@/integrations/supabase/client';

interface VolcanoConfig {
  doubaoAiUrl: string;
  textinConvertUrl: string;
}

export class VolcanoService {
  private config: VolcanoConfig;

  constructor() {
    // 环境配置，支持不同环境的API地址
    this.config = {
      doubaoAiUrl: import.meta.env.VITE_VOLCANO_DOUBAO_AI_URL || 'https://sd1mdl74gijnb7ntvsi00.apigateway-cn-guangzhou.volceapi.com/ai-service',
      textinConvertUrl: import.meta.env.VITE_VOLCANO_TEXTIN_CONVERT_URL || 'https://your-volcano-function-url/textin-convert',
    };
  }

  private async getAuthToken(): Promise<string> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('用户未登录，请先登录后再使用此功能');
    }
    return session.access_token;
  }

  private async callDoubaoAPI(action: string, data: any): Promise<any> {
    const authToken = await this.getAuthToken();

    const response = await fetch(this.config.doubaoAiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action,
        data,
        authorization: authToken
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API调用失败: ${response.status} ${errorText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result.result;
  }

  private async callTextinAPI(file: File): Promise<any> {
    const authToken = await this.getAuthToken();

    // 将文件转换为base64
    const fileBuffer = await file.arrayBuffer();
    const base64File = btoa(String.fromCharCode(...new Uint8Array(fileBuffer)));

    const response = await fetch(this.config.textinConvertUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
        'x-file-name': file.name,
        'Authorization': `Bearer ${authToken}`,
      },
      body: base64File
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`文档转换失败: ${response.status} ${errorText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  hasValidApiKey(): boolean {
    // API Key在后端管理
    return true;
  }

  setApiKey(apiKey: string) {
    // 不再需要设置API Key
    console.log('API Key现在由后端管理');
  }

  async generateTopics(domain: string): Promise<string[]> {
    try {
      const topics = await this.callDoubaoAPI('generateTopics', { domain });
      return topics;
    } catch (error) {
      console.error('生成选题失败:', error);
      throw error;
    }
  }

  async optimizeContent(
    originalText: string,
    platform: string = "",
    style: string = ""
  ): Promise<string> {
    try {
      const optimizedText = await this.callDoubaoAPI('optimize', {
        text: originalText,
        platform,
        style
      });
      return optimizedText;
    } catch (error) {
      console.error('优化内容失败:', error);
      throw error;
    }
  }

  async generateVideoScript(
    originalText: string,
    platform: string = "",
    style: string = ""
  ): Promise<string> {
    try {
      const videoScript = await this.callDoubaoAPI('generateVideoScript', {
        text: originalText,
        platform,
        style
      });
      return videoScript;
    } catch (error) {
      console.error('生成短视频脚本失败:', error);
      throw error;
    }
  }

  async generateContentByDomain(domain: string): Promise<string[]> {
    try {
      const contents = await this.callDoubaoAPI('generateContent', { domain });
      return contents;
    } catch (error) {
      console.error('生成内容失败:', error);
      throw error;
    }
  }

  async generateContentByTopic(topic: string): Promise<string[]> {
    try {
      const contents = await this.callDoubaoAPI('generateContentByTopic', { topic });
      return contents;
    } catch (error) {
      console.error('生成主题内容失败:', error);
      throw error;
    }
  }

  async generateContentByTopicStream(topic: string, onContent: (content: string) => void): Promise<string> {
    try {
      // 调用简化的流式生成API，实际上是非流式的
      const content = await this.callDoubaoAPI('generateContentByTopicStream', { topic });

      // 模拟流式输出效果
      if (content) {
        onContent(content);
        return content;
      }

      throw new Error('No content generated');
    } catch (error) {
      console.error('生成流式主题内容失败:', error);
      throw error;
    }
  }

  async popularizeContent(content: string): Promise<string> {
    try {
      const popularizedText = await this.callDoubaoAPI('popularize', { content });
      return popularizedText;
    } catch (error) {
      console.error('科普化内容失败:', error);
      throw error;
    }
  }

  async extractDocumentSummary(content: string): Promise<any> {
    try {
      const summary = await this.callDoubaoAPI('extractDocumentSummary', { content });
      return summary;
    } catch (error) {
      console.error('提取文档摘要失败:', error);
      throw error;
    }
  }

  // 新增文档转换方法
  async convertDocument(file: File): Promise<{ markdown: string }> {
    try {
      const result = await this.callTextinAPI(file);
      return result;
    } catch (error) {
      console.error('文档转换失败:', error);
      throw error;
    }
  }
}

export const volcanoService = new VolcanoService();