export declare class DoubaoService {
    private apiKey;
    private baseUrl;
    private model;
    constructor();
    private callDoubaoAPI;
    generateTopics(domain: string): Promise<string[]>;
    optimizeContent(text: string, platform: string, style: string): Promise<string>;
    generateVideoScript(text: string, platform: string, style: string): Promise<string>;
    generateContentByDomain(domain: string): Promise<string[]>;
    generateContentByTopic(topic: string): Promise<string[]>;
    popularizeContent(content: string): Promise<string>;
    extractDocumentSummary(content: string): Promise<any>;
    private parseStructuredSummary;
}
//# sourceMappingURL=doubaoService.d.ts.map