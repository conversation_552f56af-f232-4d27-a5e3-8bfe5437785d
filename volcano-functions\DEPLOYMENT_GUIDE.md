# 火山引擎云函数部署指导

## 🎉 编译完成状态

✅ **doubao-ai** 函数已成功编译
✅ **textin-convert** 函数已成功编译

两个函数的编译产物都已生成在各自的 `dist/` 目录中。

## 📦 准备部署包

### 方法一：手动打包（推荐）

#### 1. doubao-ai 函数打包
```bash
cd volcano-functions/doubao-ai
```

创建部署包，包含以下文件：
- `dist/` 目录（编译后的JavaScript文件）
- `node_modules/` 目录（依赖包）
- `package.json`

#### 2. textin-convert 函数打包
```bash
cd volcano-functions/textin-convert
```

创建部署包，包含以下文件：
- `dist/` 目录（编译后的JavaScript文件）
- `node_modules/` 目录（依赖包）
- `package.json`

### 方法二：使用压缩工具

您可以使用Windows的压缩工具或7-Zip等工具，将上述文件打包成zip格式。

## 🚀 火山引擎控制台部署步骤

### 1. 登录火山引擎控制台
- 访问：https://console.volcengine.com/
- 进入"云函数"服务

### 2. 创建 doubao-ai 函数

1. **点击"创建函数"**
2. **基本配置**：
   - 函数名称：`doubao-ai`
   - 运行时：`Node.js 18.x`
   - 入口函数：`index.handler`（重要：不是 dist/index.handler）
   - 超时时间：`300秒`
   - 内存：`512MB`

3. **上传代码**：
   - 选择"上传zip包"
   - 上传 doubao-ai 的部署包（包含根目录的 index.js 文件）

4. **环境变量配置**：
   ```
   DOUBAO_API_KEY=your_doubao_api_key_here
   ```

5. **配置HTTP触发器**：
   - 触发器类型：HTTP触发器
   - 路径：`/doubao-ai`
   - 方法：POST, OPTIONS
   - 认证方式：无认证

### 3. 创建 textin-convert 函数

1. **点击"创建函数"**
2. **基本配置**：
   - 函数名称：`textin-convert`
   - 运行时：`Node.js 18.x`
   - 入口函数：`index.handler`（重要：不是 dist/index.handler）
   - 超时时间：`300秒`
   - 内存：`1024MB`（文档处理需要更多内存）

3. **上传代码**：
   - 选择"上传zip包"
   - 上传 textin-convert 的部署包（包含根目录的 index.js 文件）

4. **环境变量配置**：
   ```
   TEXTIN_APP_ID=your_textin_app_id_here
   TEXTIN_SECRET_CODE=your_textin_secret_code_here
   ```

5. **配置HTTP触发器**：
   - 触发器类型：HTTP触发器
   - 路径：`/textin-convert`
   - 方法：POST, OPTIONS
   - 认证方式：无认证

## 🔗 获取函数URL

部署完成后，在每个函数的"触发器"页面可以看到访问URL，格式类似：
```
https://your-function-id.volcengine-api.com/doubao-ai
https://your-function-id.volcengine-api.com/textin-convert
```

## ⚙️ 更新前端配置

将获取到的URL更新到项目的 `.env` 文件中：

```bash
VITE_API_PROVIDER=volcano
VITE_VOLCANO_DOUBAO_AI_URL=https://your-function-id.volcengine-api.com/doubao-ai
VITE_VOLCANO_TEXTIN_CONVERT_URL=https://your-function-id.volcengine-api.com/textin-convert
```

## 🧪 测试验证

### 1. 测试 doubao-ai 函数
```bash
curl -X POST https://your-function-id.volcengine-api.com/doubao-ai \
  -H "Content-Type: application/json" \
  -d '{
    "action": "generateTopics",
    "data": {"domain": "婚姻家庭"},
    "authorization": "test"
  }'
```

### 2. 测试 textin-convert 函数
使用前端界面上传一个PDF文档进行测试。

## 📊 监控和日志

1. **查看函数日志**：
   - 在火山引擎控制台进入函数详情
   - 点击"监控"选项卡查看调用日志

2. **性能监控**：
   - 监控函数调用次数
   - 监控平均响应时间
   - 监控错误率

## 🔧 故障排除

### 常见问题：

1. **函数超时**：
   - 增加超时时间配置
   - 检查网络连接

2. **内存不足**：
   - 增加内存配置（特别是textin-convert）

3. **环境变量错误**：
   - 检查API密钥配置
   - 确认变量名称正确

4. **CORS错误**：
   - 确认函数返回正确的CORS头
   - 检查前端请求格式

## 📞 技术支持

如遇到部署问题，可以：
1. 查看火山引擎官方文档
2. 检查函数日志排查错误
3. 联系技术支持

---

**下一步**：完成火山引擎部署后，记得更新前端的环境变量配置！
